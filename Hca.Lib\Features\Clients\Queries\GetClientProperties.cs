using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Queries
{
    public class GetClientProperties : IQueryMany<PropertyDtoExtended>
    {
        public GetClientProperties(
            Guid clientId,
            string searchText,
            bool isArchived = false,
            bool isDeleted = false,
            ComplianceStatus complianceStatus = ComplianceStatus.ShowAll)
        {
            ClientId = clientId;
            SearchText = searchText;
            IsArchived = isArchived;
            IsDeleted = isDeleted;
            ComplianceStatus = complianceStatus;
        }

        public GetClientProperties(
            Guid clientId, 
            Guid? siteId = null, 
            bool isArchived = false, 
            bool isDeleted = false) 
        {
            ClientId = clientId;
            SiteId = siteId;
            IsArchived = isArchived;
            IsDeleted = isDeleted;
        }

        public Guid ClientId { get; }
        public string SearchText { get; }
        public Guid? SiteId { get; }
        public bool IsArchived { get; }
        public bool IsDeleted { get; }
        public ComplianceStatus ComplianceStatus { get; }
    }

    public enum ComplianceStatus
    {
        ShowAll = 0,
        Compliant = 1,
        NotCompliant = 2,
        DueWithinOneMonth = 3,
        NoDocumentsOrAcms = 4,
    }

    public class GetClientPropertiesHandler : DapperRequestHandler<GetClientProperties, DtoSet<PropertyDtoExtended>>
    {
        public GetClientPropertiesHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<DtoSet<PropertyDtoExtended>> OnHandleAsync(IDbHelper db, GetClientProperties request)
        {
            var sql = $"SELECT {TableNames.Properties}.*, " +
                $"   {nameof(PropertyDtoExtended.SiteName)}, " +
                $"  (SELECT " +
                $"      MAX({nameof(PropertyDocumentDto.NextInspection)}) " +
                $"      FROM {TableNames.PropertyDocuments} " +
                $"      WHERE {nameof(PropertyDocumentDto.PropertyId)} = {TableNames.Properties}.{nameof(PropertyDtoExtended.Id)}) " +
                $"      AS {nameof(PropertyDtoExtended.NextInspection)}, " +
                $"   COALESCE(PropertyAddress.{nameof(AddressDto.Id)}, SiteAddress.{nameof(AddressDto.Id)}) AS pkAddressId, " +
                $"   COALESCE(PropertyAddress.{nameof(AddressDto.StreetName)}, SiteAddress.{nameof(AddressDto.StreetName)}) AS {nameof(AddressDto.StreetName)}, " +
                $"   COALESCE(PropertyAddress.{nameof(AddressDto.Town)}, SiteAddress.{nameof(AddressDto.Town)}) AS {nameof(AddressDto.Town)}, " +
                $"   COALESCE(PropertyAddress.{nameof(AddressDto.City)}, SiteAddress.{nameof(AddressDto.City)}) AS {nameof(AddressDto.City)}, " +
                $"   COALESCE(PropertyAddress.{nameof(AddressDto.County)}, SiteAddress.{nameof(AddressDto.County)}) AS {nameof(AddressDto.County)}, " +
                $"   COALESCE(PropertyAddress.{nameof(AddressDto.Postcode)}, SiteAddress.{nameof(AddressDto.Postcode)}) AS {nameof(AddressDto.Postcode)}, " +
                $"   COALESCE(PropertyAddress.{nameof(AddressDto.Country)}, SiteAddress.{nameof(AddressDto.Country)}) AS {nameof(AddressDto.Country)}, " +
                $"   COALESCE(PropertyAddress.{nameof(AddressDto.Lat)}, SiteAddress.{nameof(AddressDto.Lat)}) AS {nameof(AddressDto.Lat)}, " +
                $"   COALESCE(PropertyAddress.{nameof(AddressDto.Lon)}, SiteAddress.{nameof(AddressDto.Lon)}) AS {nameof(AddressDto.Lon)}, " +
                $"   (SELECT MAX({nameof(PropertyDocumentDto.NextInspection)}) FROM {TableNames.PropertyDocuments} " +
                $"      WHERE {TableNames.PropertyDocuments}.{nameof(PropertyDocumentDto.PropertyId)} = {TableNames.Properties}.{nameof(PropertyDtoExtended.Id)}) " +
                $"FROM {TableNames.Properties} " +
                $"LEFT JOIN {TableNames.Addresses} PropertyAddress ON {nameof(PropertyDtoExtended.AddressId)} = PropertyAddress.{nameof(AddressDto.Id)} " +
                $"LEFT JOIN {TableNames.Sites} ON {nameof(PropertyDtoExtended.SiteId)} = {TableNames.Sites}.{nameof(SiteDtoExtended.Id)} " +
                $"LEFT JOIN {TableNames.Addresses} SiteAddress ON {TableNames.Sites}.{nameof(SiteDtoExtended.AddressId)} = SiteAddress.{nameof(AddressDto.Id)} " +
                $"WHERE {TableNames.Properties}.{nameof(PropertyDtoExtended.ClientId)}=@{nameof(request.ClientId)} " +
                $"AND {TableNames.Properties}.Archived IS {(request.IsArchived ? "NOT NULL" : "NULL")} " +
                $"AND {TableNames.Properties}.Deleted IS {(request.IsDeleted ? "NOT NULL" : "NULL")} ";

            // filter by NextInspection request parameter, ShowAll = no filter, NotCompliant = NextInspection < today,
            // DueWithinOneMonth = NextInspection < today + 1 month, Compliant = NextInspection >= today + 1 month
            if (request.ComplianceStatus != ComplianceStatus.ShowAll)
            {
                var today = DateTime.Today;
                var oneMonth = today.AddMonths(1);
                sql += request.ComplianceStatus switch
                {
                    ComplianceStatus.Compliant => $"AND (SELECT MAX({nameof(PropertyDocumentDto.NextInspection)}) FROM {TableNames.PropertyDocuments} WHERE {TableNames.PropertyDocuments}.{nameof(PropertyDocumentDto.PropertyId)} = {TableNames.Properties}.{nameof(PropertyDtoExtended.Id)}) >= '{today:yyyy-MM-dd}' ",
                    ComplianceStatus.NotCompliant => $"AND (SELECT MAX({nameof(PropertyDocumentDto.NextInspection)}) FROM {TableNames.PropertyDocuments} WHERE {TableNames.PropertyDocuments}.{nameof(PropertyDocumentDto.PropertyId)} = {TableNames.Properties}.{nameof(PropertyDtoExtended.Id)}) < '{today:yyyy-MM-dd}' ",
                    ComplianceStatus.DueWithinOneMonth => $"AND (SELECT MAX({nameof(PropertyDocumentDto.NextInspection)}) FROM {TableNames.PropertyDocuments} WHERE {TableNames.PropertyDocuments}.{nameof(PropertyDocumentDto.PropertyId)} = {TableNames.Properties}.{nameof(PropertyDtoExtended.Id)}) BETWEEN '{today:yyyy-MM-dd}' AND '{oneMonth:yyyy-MM-dd}' ",
                    ComplianceStatus.NoDocumentsOrAcms => $"AND NOT EXISTS (SELECT 1 FROM {TableNames.PropertyDocuments} WHERE {TableNames.PropertyDocuments}.{nameof(PropertyDocumentDto.PropertyId)} = {TableNames.Properties}.{nameof(PropertyDtoExtended.Id)}) ",
                    _ => throw new ArgumentException("Invalid compliance status", nameof(request.ComplianceStatus)),
                };
            }

            if (request.SiteId.HasValue)
            {
                sql += $" AND {TableNames.Properties}.{nameof(PropertyDtoExtended.SiteId)}=@{nameof(request.SiteId)} ";
            }

            // Use a multi-column sort approach for better natural sorting of Unit values
            sql +=
                $"ORDER BY " +
                $"COALESCE(PropertyCode, ''), " +
                $"CASE WHEN Unit IS NULL OR Unit = '' THEN 0 ELSE 1 END, " + // NULL/empty units first
                $"CASE " +
                $"  WHEN Unit IS NULL OR Unit = '' THEN 0 " +
                $"  WHEN PATINDEX('%[0-9]%', Unit) = 1 THEN 1 " + // Units starting with numbers: sort first
                $"  ELSE 2 " + // Units starting with letters: sort last
                $"END, " +
                $"CASE " +
                $"  WHEN Unit IS NULL OR Unit = '' THEN 0 " +
                $"  WHEN PATINDEX('%[0-9]%', Unit) = 1 " + // Units starting with numbers
                $"    THEN CAST(SUBSTRING(Unit, 1, " +
                $"         CASE WHEN PATINDEX('%[^0-9]%', Unit) = 0 " +
                $"              THEN LEN(Unit) " +
                $"              ELSE PATINDEX('%[^0-9]%', Unit) - 1 " +
                $"         END) AS INT) " +
                $"  ELSE 0 " + // Letter-prefixed units - will be sorted by letter first, then number
                $"END, " +
                $"CASE " +
                $"  WHEN Unit IS NULL OR Unit = '' THEN '' " +
                $"  WHEN PATINDEX('%[0-9]%', Unit) = 1 " + // Units starting with numbers - get suffix
                $"    THEN SUBSTRING(Unit, " +
                $"         CASE WHEN PATINDEX('%[^0-9]%', Unit) = 0 " +
                $"              THEN LEN(Unit) + 1 " +
                $"              ELSE PATINDEX('%[^0-9]%', Unit) " +
                $"         END, LEN(Unit)) " +
                $"  ELSE SUBSTRING(Unit, 1, CASE WHEN PATINDEX('%[0-9]%', Unit) - 1 < 1 THEN 1 ELSE PATINDEX('%[0-9]%', Unit) - 1 END) " + // Letter-prefixed units - get letter prefix FIRST
                $"END, " +
                $"CASE " +
                $"  WHEN Unit IS NULL OR Unit = '' THEN 0 " +
                $"  WHEN PATINDEX('%[0-9]%', Unit) = 1 THEN 0 " + // Number-prefixed units already sorted
                $"  ELSE " + // Letter-prefixed units - extract numeric part AFTER letter sorting
                $"    CASE WHEN PATINDEX('%[0-9]%', Unit) > 0 " +
                $"         THEN CAST(SUBSTRING(Unit, PATINDEX('%[0-9]%', Unit), " +
                $"              CASE WHEN PATINDEX('%[^0-9]%', SUBSTRING(Unit, PATINDEX('%[0-9]%', Unit), LEN(Unit))) = 0 " +
                $"                   THEN CASE WHEN LEN(Unit) - PATINDEX('%[0-9]%', Unit) + 1 < 1 THEN 1 ELSE LEN(Unit) - PATINDEX('%[0-9]%', Unit) + 1 END " +
                $"                   ELSE CASE WHEN PATINDEX('%[^0-9]%', SUBSTRING(Unit, PATINDEX('%[^0-9]%', Unit), LEN(Unit))) - 1 < 1 THEN 1 ELSE PATINDEX('%[^0-9]%', SUBSTRING(Unit, PATINDEX('%[0-9]%', Unit), LEN(Unit))) - 1 END " +
                $"              END) AS INT) " +
                $"         ELSE 999999 " + // No numbers found, sort last
                $"    END " +
                $"END, " +
                $"Unit, " + // Final sort by full unit string for complex cases like "6-W7"
                $"COALESCE(SiteName, ''), " +
                $"COALESCE(PropertyAddress.StreetName, SiteAddress.StreetName, ''), " +
                $"COALESCE(Custom, ''), " +
                $"COALESCE(PropertyAddress.Town, SiteAddress.Town, ''), " +
                $"COALESCE(PropertyAddress.City, SiteAddress.City, ''), " +
                $"COALESCE(PropertyAddress.County, SiteAddress.County, ''), " +
                $"COALESCE(PropertyAddress.Country, SiteAddress.Country, ''), " +
                $"COALESCE(PropertyAddress.Postcode, SiteAddress.Postcode, '')";

            var items = await db.QueryAsync<PropertyDtoExtended, AddressDto>(
                sql,
                (p, a) => { p.Address = a; return p; },
                "pkAddressId",
                request);

            if (!request.SearchText.IsNullOrWhiteSpace())
            {
                items = items.Where(i => i.GetDisplayText().Contains(request.SearchText, StringComparison.OrdinalIgnoreCase));
            }

            return DtoSet.From(items);
        }
    }
}
